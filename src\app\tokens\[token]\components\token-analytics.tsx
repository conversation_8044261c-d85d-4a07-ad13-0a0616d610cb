"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { formatWeiToEther } from "@/helpers/format"
import { useStatsAnalyticsTokens } from "@/hooks/useTokens"
import { DailyAnalytics, TokenResult } from "@/types/tokens"
import { Download, Info } from "lucide-react"
import { useMemo, useState } from "react"
import {
  Area,
  AreaChart,
  CartesianGrid,
  Legend,
  Line,
  LineChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts"

interface TokenAnalyticsProps {
  tokenAddress: string
  tokenDetail: TokenResult | undefined
}

interface ChartDataPoint {
  date: string
  transfersCount: number
  transfersAmount: number
  uniqueTotalCount: number
  uniqueSendersCount: number
  uniqueReceiversCount: number
  formattedDate: string
}

export function TokenAnalytics({
  tokenAddress,
  tokenDetail,
}: TokenAnalyticsProps) {
  const [activeMetrics, setActiveMetrics] = useState({
    transfersAmount: true,
    transfersCount: true,
    uniqueReceiversCount: true,
    uniqueSendersCount: true,
    uniqueTotalCount: true,
  })

  const {
    data: analyticsData,
    isLoading,
    isError,
  } = useStatsAnalyticsTokens(tokenAddress)

  const chartData = useMemo(() => {
    if (!analyticsData) return []

    // Handle both array and single object responses
    const result = Array.isArray(analyticsData) ? analyticsData[0] : analyticsData
    if (!result?.dailyAnalytics) return []

    const dataPoints: ChartDataPoint[] = Object.entries(result.dailyAnalytics)
      .map(([dateStr, analytics]: [string, DailyAnalytics]) => {
        // Handle scientific notation in transfersAmount
        const amountStr = analytics.transfersAmount.toString()
        const amountBigInt = BigInt(parseFloat(amountStr).toFixed(0))

        return {
          date: dateStr,
          transfersCount: analytics.transfersCount,
          transfersAmount: tokenDetail
            ? parseFloat(formatWeiToEther(amountBigInt.toString(), tokenDetail.decimals))
            : parseFloat(formatWeiToEther(amountBigInt.toString())),
          uniqueTotalCount: analytics.uniqueTotalCount,
          uniqueSendersCount: analytics.uniqueSendersCount,
          uniqueReceiversCount: analytics.uniqueReceiversCount,
          formattedDate: new Date(dateStr).toLocaleDateString("en-US", {
            month: "short",
            day: "numeric",
            year: "numeric",
          }),
        }
      })
      .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())

    return dataPoints
  }, [analyticsData, tokenDetail])

  const handleDownloadCSV = () => {
    if (chartData.length === 0) return

    const headers = [
      "Date(UTC)",
      "UnixTimeStamp",
      "Transfer Amount",
      "Transfer Count",
      "Unique Receivers",
      "Unique Senders",
      "Total Uniques",
    ]

    const csvData = chartData.map((item) => [
      item.formattedDate,
      new Date(item.date).getTime() / 1000,
      item.transfersAmount,
      item.transfersCount,
      item.uniqueReceiversCount,
      item.uniqueSendersCount,
      item.uniqueTotalCount,
    ])

    const csvContent = [headers, ...csvData]
      .map((row) => row.join(","))
      .join("\n")

    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" })
    const link = document.createElement("a")
    const url = URL.createObjectURL(blob)
    link.setAttribute("href", url)
    link.setAttribute("download", `token-${tokenAddress}-analytics.csv`)
    link.style.visibility = "hidden"
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  if (isError) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col items-center justify-center p-6 text-center">
            <Info className="h-10 w-10 text-red-500 mb-4" />
            <h3 className="text-lg font-semibold">Error Loading Analytics</h3>
            <p className="text-sm text-muted-foreground mt-2">
              Unable to load analytics data for this token.
            </p>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Token Analytics</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-[400px] w-full" />
          </div>
        </CardContent>
      </Card>
    )
  }

  if (chartData.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Token Analytics</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col items-center justify-center p-6 text-center">
            <Info className="h-10 w-10 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold">No Analytics Data</h3>
            <p className="text-sm text-muted-foreground mt-2">
              No analytics data is available for this token yet.
            </p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div className="flex items-center space-x-2">
          <CardTitle>Token Analytics</CardTitle>
          <Button variant="ghost" size="icon" className="h-8 w-8">
            <Info className="h-4 w-4" />
          </Button>
        </div>
        <Button
          variant="outline"
          size="sm"
          className="flex items-center"
          onClick={handleDownloadCSV}
          disabled={chartData.length === 0}
        >
          <Download className="mr-2 h-4 w-4" />
          Download CSV
        </Button>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="overview" className="space-y-4">
          <TabsList>
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="transfers">Transfers</TabsTrigger>
            <TabsTrigger value="users">Users</TabsTrigger>
          </TabsList>

          <TabsContent value="overview">
            <div className="h-[500px]">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={chartData}
                  margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
                >
                  <CartesianGrid strokeDasharray="3 3" vertical={false} />
                  <XAxis
                    dataKey="formattedDate"
                    tickLine={false}
                    axisLine={false}
                    tick={{ fontSize: 12 }}
                  />
                  <YAxis
                    yAxisId="left"
                    orientation="left"
                    tickLine={false}
                    axisLine={false}
                    tick={{ fontSize: 12 }}
                    label={{
                      value: "Count",
                      angle: -90,
                      position: "insideLeft",
                      style: { textAnchor: "middle" },
                    }}
                  />
                  <YAxis
                    yAxisId="right"
                    orientation="right"
                    tickLine={false}
                    axisLine={false}
                    tick={{ fontSize: 12 }}
                    label={{
                      value: `Amount (${tokenDetail?.symbol || "Tokens"})`,
                      angle: 90,
                      position: "insideRight",
                      style: { textAnchor: "middle" },
                    }}
                  />
                  <Tooltip
                    content={
                      <CustomTooltip tokenSymbol={tokenDetail?.symbol} />
                    }
                  />
                  <Legend />

                  {activeMetrics.transfersAmount && (
                    <Area
                      yAxisId="right"
                      type="monotone"
                      dataKey="transfersAmount"
                      stroke="#3498db"
                      fill="#3498db"
                      fillOpacity={0.2}
                      name="Transfer Amount"
                    />
                  )}

                  {activeMetrics.transfersCount && (
                    <Line
                      yAxisId="left"
                      type="monotone"
                      dataKey="transfersCount"
                      stroke="#2ecc71"
                      strokeWidth={2}
                      dot={false}
                      name="Transfer Count"
                    />
                  )}

                  {activeMetrics.uniqueReceiversCount && (
                    <Line
                      yAxisId="left"
                      type="monotone"
                      dataKey="uniqueReceiversCount"
                      stroke="#f39c12"
                      strokeWidth={2}
                      dot={false}
                      name="Unique Receivers"
                    />
                  )}

                  {activeMetrics.uniqueSendersCount && (
                    <Line
                      yAxisId="left"
                      type="monotone"
                      dataKey="uniqueSendersCount"
                      stroke="#e74c3c"
                      strokeWidth={2}
                      dot={false}
                      name="Unique Senders"
                    />
                  )}

                  {activeMetrics.uniqueTotalCount && (
                    <Line
                      yAxisId="left"
                      type="monotone"
                      dataKey="uniqueTotalCount"
                      stroke="#9b59b6"
                      strokeWidth={2}
                      dot={false}
                      name="Total Uniques"
                    />
                  )}
                </LineChart>
              </ResponsiveContainer>
            </div>
          </TabsContent>

          <TabsContent value="transfers">
            <div className="h-[500px]">
              <ResponsiveContainer width="100%" height="100%">
                <AreaChart
                  data={chartData}
                  margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
                >
                  <CartesianGrid strokeDasharray="3 3" vertical={false} />
                  <XAxis
                    dataKey="formattedDate"
                    tickLine={false}
                    axisLine={false}
                    tick={{ fontSize: 12 }}
                  />
                  <YAxis
                    yAxisId="left"
                    orientation="left"
                    tickLine={false}
                    axisLine={false}
                    tick={{ fontSize: 12 }}
                    label={{
                      value: "Transfer Count",
                      angle: -90,
                      position: "insideLeft",
                      style: { textAnchor: "middle" },
                    }}
                  />
                  <YAxis
                    yAxisId="right"
                    orientation="right"
                    tickLine={false}
                    axisLine={false}
                    tick={{ fontSize: 12 }}
                    label={{
                      value: `Amount (${tokenDetail?.symbol || "Tokens"})`,
                      angle: 90,
                      position: "insideRight",
                      style: { textAnchor: "middle" },
                    }}
                  />
                  <Tooltip
                    content={
                      <CustomTooltip tokenSymbol={tokenDetail?.symbol} />
                    }
                  />
                  <Legend />

                  <Area
                    yAxisId="right"
                    type="monotone"
                    dataKey="transfersAmount"
                    stroke="#3498db"
                    fill="#3498db"
                    fillOpacity={0.3}
                    name="Transfer Amount"
                  />

                  <Line
                    yAxisId="left"
                    type="monotone"
                    dataKey="transfersCount"
                    stroke="#2ecc71"
                    strokeWidth={3}
                    dot={false}
                    name="Transfer Count"
                  />
                </AreaChart>
              </ResponsiveContainer>
            </div>
          </TabsContent>

          <TabsContent value="users">
            <div className="h-[500px]">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={chartData}
                  margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
                >
                  <CartesianGrid strokeDasharray="3 3" vertical={false} />
                  <XAxis
                    dataKey="formattedDate"
                    tickLine={false}
                    axisLine={false}
                    tick={{ fontSize: 12 }}
                  />
                  <YAxis
                    tickLine={false}
                    axisLine={false}
                    tick={{ fontSize: 12 }}
                    label={{
                      value: "Unique Users",
                      angle: -90,
                      position: "insideLeft",
                      style: { textAnchor: "middle" },
                    }}
                  />
                  <Tooltip
                    content={
                      <CustomTooltip tokenSymbol={tokenDetail?.symbol} />
                    }
                  />
                  <Legend />

                  <Line
                    type="monotone"
                    dataKey="uniqueReceiversCount"
                    stroke="#f39c12"
                    strokeWidth={2}
                    dot={false}
                    name="Unique Receivers"
                  />

                  <Line
                    type="monotone"
                    dataKey="uniqueSendersCount"
                    stroke="#e74c3c"
                    strokeWidth={2}
                    dot={false}
                    name="Unique Senders"
                  />

                  <Line
                    type="monotone"
                    dataKey="uniqueTotalCount"
                    stroke="#9b59b6"
                    strokeWidth={2}
                    dot={false}
                    name="Total Uniques"
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}

interface CustomTooltipProps {
  active?: boolean
  payload?: any[]
  label?: string
  tokenSymbol?: string
}

function CustomTooltip({
  active,
  payload,
  label,
  tokenSymbol,
}: CustomTooltipProps) {
  if (active && payload && payload.length) {
    return (
      <div className="bg-white p-4 border rounded-lg shadow-lg">
        <p className="font-semibold mb-2">{label}</p>
        {payload.map((entry, index) => (
          <p key={index} style={{ color: entry.color }} className="text-sm">
            {entry.name}:{" "}
            {entry.name === "Transfer Amount"
              ? `${entry.value.toLocaleString()} ${tokenSymbol || ""}`
              : entry.value.toLocaleString()}
          </p>
        ))}
      </div>
    )
  }
  return null
}
