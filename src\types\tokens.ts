import { TokenDetails } from "./transactions"

export interface BaseTokenInfo {
  address: string
  name: string
  symbol: string
  logo: string | null
  decimals: number
}

export interface Token {
  address: string
  createdAt: string
  decimals: number
  enabled: boolean
  logo: string | null
  name: string
  owner: number
  symbol: string
  totalSupply: string
  type: string
  updatedAt: string
}
export interface TokenListResponse {
  jsonrpc: string
  id: number
  result: Result[]
}

export interface Result {
  metadata: Metadata
  holders_count: number
  total_supply: string
  holdersCount: number
}

export interface Metadata {
  description: string
  denom_units: DenomUnit[]
  base: string
  display: string
  name: string
  symbol: string
  decimals: number
  logo: string
  contract_address: string
}

export interface DenomUnit {
  denom: string
  exponent?: number
}

export interface TokenBalance {
  address: string
  denom: string
  symbol: string
  balance: string
  balanceUI: string
  decimals: number
  description: string
}

export interface TokenBalanceListResponse {
  result: {
    Balances: TokenBalance[]
    TotalCount: number
  }
}

export interface TransferTokenEvent {
  id: number
  createdAt: string
  updatedAt: string
  blockNumber: number
  type: string
  from: string
  transactionHash: string
  to: string
  amount: string
  value: string
  tokenAddress: string
  tokenId: any
  functionSignature: string
  timestamp: string
  tokenDetails: TokenDetails
  nftDetails: any
}

export interface TransferTokenEventListResponse {
  data: TransferTokenEvent[]
  metadata: {
    limit: number
    page: number
    total: number
    totalPages: number
  }
}

export interface BalanceResponse {
  result: string
}

export interface TokenHolder {
  id: number
  createdAt: string
  updatedAt: string
  address: string
  tokenAddress: string
  balance: string
  percentage: string
  txnCount: number
}

export interface TokenHolderListResponse {
  data: TokenHolder[]
  metadata: {
    limit: number
    page: number
    total: number
    totalPages: number
  }
}

export interface ChainStatsResponse {
  id: number
  latestBlock: string
  marketCap: number
  medGasPrice: number
  price: number
  totalTransactions: string
  tps: number
  transactionHistory: {
    count: number
    date: string
  }[]
  updatedAt: string
}

export interface TokenResult {
  id: number
  createdAt: string
  updatedAt: string
  address: string
  name: string
  symbol: string
  decimals: any
  totalSupply: string
  logo: any
  type: TokenType
  enabled: boolean
  owner: number
  isCreationModule: boolean
  isNft: boolean
  nftType: string
  holdersCount: number
}

export interface TokenDetailResponse {
  jsonrpc: string
  id: number
  result: TokenResult
}

export enum TokenType {
  HRC20 = "HRC20",
  ERC1155 = "ERC1155",
  ERC721 = "ERC721",
  ERC20 = "ERC20",
}

export interface StatsAnalyticsTokensResponse {
  jsonrpc: string
  id: number
  result: StatsAnalyticsResult
}

export interface DailyAnalytics {
  transfersCount: number
  transfersAmount: string
  uniqueTotalCount: number
  uniqueSendersCount: number
  uniqueReceiversCount: number
}

export interface TokenDetailsAnalytics extends BaseTokenInfo {}

export interface StatsAnalyticsResult {
  id: number
  createdAt: string
  updatedAt: string
  tokenAddress: string
  dailyAnalytics: Record<string, DailyAnalytics>
  lastUpdatedDate: string
  tokenDetails: TokenDetailsAnalytics
}
